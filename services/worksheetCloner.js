const ExcelJS = require('exceljs');

class WorksheetCloner {
    constructor() {
        this.imageMap = new Map(); // To track images and their references
    }

    /**
     * Clone a worksheet multiple times within the same workbook
     * @param {ExcelJS.Workbook} workbook - The workbook containing the source worksheet
     * @param {string} sourceWorksheetName - Name of the source worksheet to clone
     * @param {number} numberOfCopies - Number of copies to create (default: 3)
     * @returns {Promise<Array>} - Array of cloned worksheet names
     */
    async cloneWorksheet(workbook, sourceWorksheetName, numberOfCopies = 3) {
        try {
            const sourceWorksheet = workbook.getWorksheet(sourceWorksheetName);
            if (!sourceWorksheet) {
                throw new Error(`Source worksheet '${sourceWorksheetName}' not found`);
            }

            console.log(`Starting to clone worksheet '${sourceWorksheetName}' ${numberOfCopies} times`);
            
            const clonedWorksheetNames = [];

            for (let i = 1; i <= numberOfCopies; i++) {
                const clonedName = `${sourceWorksheetName}_Copy_${i}`;
                console.log(`Creating copy ${i}: ${clonedName}`);
                
                const clonedWorksheet = await this.createWorksheetCopy(
                    workbook, 
                    sourceWorksheet, 
                    clonedName
                );
                
                clonedWorksheetNames.push(clonedName);
            }

            console.log(`Successfully created ${numberOfCopies} copies of '${sourceWorksheetName}'`);
            return clonedWorksheetNames;
        } catch (error) {
            console.error('Error cloning worksheet:', error);
            throw new Error(`Failed to clone worksheet: ${error.message}`);
        }
    }

    /**
     * Create a single copy of a worksheet
     * @param {ExcelJS.Workbook} workbook - The workbook
     * @param {ExcelJS.Worksheet} sourceWorksheet - Source worksheet to copy
     * @param {string} newName - Name for the new worksheet
     * @returns {Promise<ExcelJS.Worksheet>} - The cloned worksheet
     */
    async createWorksheetCopy(workbook, sourceWorksheet, newName) {
        // Create new worksheet
        const clonedWorksheet = workbook.addWorksheet(newName);

        // Copy basic worksheet properties
        await this.copyWorksheetProperties(sourceWorksheet, clonedWorksheet);

        // Copy all cell data, formatting, and formulas
        await this.copyCellData(sourceWorksheet, clonedWorksheet);

        // Copy merged cell ranges
        await this.copyMergedCells(sourceWorksheet, clonedWorksheet);

        // Copy images (this is the most complex part)
        await this.copyImages(workbook, sourceWorksheet, clonedWorksheet);

        // Copy column and row properties
        await this.copyColumnRowProperties(sourceWorksheet, clonedWorksheet);

        return clonedWorksheet;
    }

    /**
     * Copy basic worksheet properties
     * @param {ExcelJS.Worksheet} source - Source worksheet
     * @param {ExcelJS.Worksheet} target - Target worksheet
     */
    async copyWorksheetProperties(source, target) {
        try {
            // Copy worksheet properties
            if (source.properties) {
                target.properties = { ...source.properties };
            }

            // Copy page setup
            if (source.pageSetup) {
                target.pageSetup = { ...source.pageSetup };
            }

            // Copy header and footer
            if (source.headerFooter) {
                target.headerFooter = { ...source.headerFooter };
            }

            // Copy views
            if (source.views) {
                target.views = JSON.parse(JSON.stringify(source.views));
            }

            console.log(`Copied worksheet properties for ${target.name}`);
        } catch (error) {
            console.warn('Error copying worksheet properties:', error);
        }
    }

    /**
     * Copy all cell data including values, formulas, and formatting
     * @param {ExcelJS.Worksheet} source - Source worksheet
     * @param {ExcelJS.Worksheet} target - Target worksheet
     */
    async copyCellData(source, target) {
        try {
            source.eachRow((row, rowNumber) => {
                const targetRow = target.getRow(rowNumber);
                
                // Copy row properties
                if (row.height) targetRow.height = row.height;
                if (row.hidden) targetRow.hidden = row.hidden;
                if (row.outlineLevel) targetRow.outlineLevel = row.outlineLevel;

                row.eachCell((cell, colNumber) => {
                    const targetCell = targetRow.getCell(colNumber);
                    
                    // Copy cell value
                    if (cell.value !== null && cell.value !== undefined) {
                        targetCell.value = cell.value;
                    }

                    // Copy cell formatting
                    if (cell.style) {
                        targetCell.style = JSON.parse(JSON.stringify(cell.style));
                    }

                    // Copy data validation
                    if (cell.dataValidation) {
                        targetCell.dataValidation = JSON.parse(JSON.stringify(cell.dataValidation));
                    }

                    // Copy hyperlink
                    if (cell.hyperlink) {
                        targetCell.hyperlink = cell.hyperlink;
                    }

                    // Copy note/comment
                    if (cell.note) {
                        targetCell.note = cell.note;
                    }
                });

                targetRow.commit();
            });

            console.log(`Copied cell data for ${target.name}`);
        } catch (error) {
            console.error('Error copying cell data:', error);
            throw error;
        }
    }

    /**
     * Copy merged cell ranges from source to target worksheet
     * @param {ExcelJS.Worksheet} source - Source worksheet
     * @param {ExcelJS.Worksheet} target - Target worksheet
     */
    async copyMergedCells(source, target) {
        try {
            // Get merged cell ranges from source worksheet
            const mergedCells = [];

            // ExcelJS stores merged cells in the worksheet model
            if (source.model && source.model.merges) {
                source.model.merges.forEach(merge => {
                    mergedCells.push(merge);
                });
            }

            // Apply merged cells to target worksheet
            mergedCells.forEach(merge => {
                try {
                    target.mergeCells(merge);
                    console.log(`Merged cells: ${merge}`);
                } catch (error) {
                    console.warn(`Failed to merge cells ${merge}:`, error.message);
                }
            });

            console.log(`Copied ${mergedCells.length} merged cell ranges for ${target.name}`);
        } catch (error) {
            console.warn('Error copying merged cells:', error);
        }
    }

    /**
     * Copy images from source to target worksheet
     * @param {ExcelJS.Workbook} workbook - The workbook
     * @param {ExcelJS.Worksheet} source - Source worksheet
     * @param {ExcelJS.Worksheet} target - Target worksheet
     */
    async copyImages(workbook, source, target) {
        try {
            // Get images from source worksheet
            let images = [];

            // Try to get images using different methods depending on ExcelJS version
            if (source.getImages) {
                images = source.getImages();
            } else if (source.model && source.model.drawing && source.model.drawing.anchors) {
                // Alternative method for accessing images
                images = source.model.drawing.anchors;
            }

            if (images.length === 0) {
                console.log(`No images found in ${source.name}`);
                return;
            }

            console.log(`Found ${images.length} images in ${source.name}`);

            // Copy each image to the target worksheet
            for (const image of images) {
                try {
                    await this.copyImage(workbook, image, target);
                } catch (error) {
                    console.warn(`Failed to copy image:`, error.message);
                }
            }

            console.log(`Copied images for ${target.name}`);
        } catch (error) {
            console.warn('Error copying images:', error);
        }
    }

    /**
     * Copy a single image to target worksheet
     * @param {ExcelJS.Workbook} workbook - The workbook
     * @param {Object} imageInfo - Image information from source
     * @param {ExcelJS.Worksheet} target - Target worksheet
     */
    async copyImage(workbook, imageInfo, target) {
        try {
            // The image copying process depends on how ExcelJS handles images
            // This is a complex operation that may require different approaches

            if (imageInfo.imageId) {
                // Method 1: Using image ID
                const imageId = workbook.addImage({
                    base64: imageInfo.base64 || imageInfo.buffer,
                    extension: imageInfo.extension || 'png'
                });

                target.addImage(imageId, {
                    tl: imageInfo.range.tl || { col: 0, row: 0 },
                    br: imageInfo.range.br || { col: 1, row: 1 },
                    editAs: imageInfo.range.editAs || 'oneCell'
                });
            } else if (imageInfo.buffer || imageInfo.base64) {
                // Method 2: Using buffer or base64 data
                const imageId = workbook.addImage({
                    buffer: imageInfo.buffer,
                    extension: imageInfo.extension || 'png'
                });

                target.addImage(imageId, imageInfo.range || {
                    tl: { col: 0, row: 0 },
                    br: { col: 1, row: 1 }
                });
            }

            console.log(`Copied image to ${target.name}`);
        } catch (error) {
            console.warn('Error copying individual image:', error);
        }
    }

    /**
     * Copy column and row properties
     * @param {ExcelJS.Worksheet} source - Source worksheet
     * @param {ExcelJS.Worksheet} target - Target worksheet
     */
    async copyColumnRowProperties(source, target) {
        try {
            // Copy column properties
            source.columns.forEach((column, index) => {
                if (column) {
                    const targetColumn = target.getColumn(index + 1);
                    if (column.width) targetColumn.width = column.width;
                    if (column.hidden) targetColumn.hidden = column.hidden;
                    if (column.outlineLevel) targetColumn.outlineLevel = column.outlineLevel;
                }
            });

            console.log(`Copied column and row properties for ${target.name}`);
        } catch (error) {
            console.warn('Error copying column/row properties:', error);
        }
    }

    /**
     * Get detailed information about a worksheet including complex features
     * @param {ExcelJS.Worksheet} worksheet - The worksheet to analyze
     * @returns {Object} - Detailed worksheet information
     */
    getWorksheetDetails(worksheet) {
        const details = {
            name: worksheet.name,
            rowCount: worksheet.rowCount,
            columnCount: worksheet.columnCount,
            actualRowCount: worksheet.actualRowCount,
            actualColumnCount: worksheet.actualColumnCount,
            mergedCells: [],
            images: [],
            hasFormulas: false,
            hasDataValidation: false,
            hasHyperlinks: false
        };

        try {
            // Check for merged cells
            if (worksheet.model && worksheet.model.merges) {
                details.mergedCells = worksheet.model.merges;
            }

            // Check for images
            if (worksheet.getImages) {
                details.images = worksheet.getImages();
            }

            // Analyze cells for formulas, validation, hyperlinks
            worksheet.eachRow((row, rowNumber) => {
                row.eachCell((cell, colNumber) => {
                    if (cell.formula) details.hasFormulas = true;
                    if (cell.dataValidation) details.hasDataValidation = true;
                    if (cell.hyperlink) details.hasHyperlinks = true;
                });
            });

        } catch (error) {
            console.warn('Error getting worksheet details:', error);
        }

        return details;
    }
}

module.exports = WorksheetCloner;
